<script lang="ts">
  import { createEventDispatcher } from "svelte";
  import {
    BottomSheet,
    openBottomSheet,
    closeBottomSheet,
  } from "@CDNA-Technologies/svelte-vitals/components/bottom-sheet";
  import PrimaryButton from "@CDNA-Technologies/svelte-vitals/components/primary-button";
  import SecondaryButton from "@CDNA-Technologies/svelte-vitals/components/secondary-button";

  export let modalId = "traveller-selection";
  export let initialAdults = 1;
  export let initialChildren = 0;
  export let initialInfants = 0;
  export let initialClass = "Business Class";

  let adults = initialAdults;
  let children = initialChildren;
  let infants = initialInfants;
  let selectedClass = initialClass;

  const dispatch = createEventDispatcher();

  // Travel class options
  const travelClasses = [
    { id: "economy", label: "Economy Class" },
    { id: "premium", label: "Premium Economy Class" },
    { id: "business", label: "Business Class" },
  ];

  function getTotalPassengers() {
    return adults + children + infants;
  }

  function incrementAdults() {
    if (adults < 9 && getTotalPassengers() < 10) {
      adults++;
    }
  }

  function decrementAdults() {
    if (adults > 1) {
      adults--;
      // Ensure infants don't exceed adults
      if (infants > adults) {
        infants = adults;
      }
    }
  }

  function incrementChildren() {
    if (getTotalPassengers() < 10 && adults + children <= 9) {
      children++;
    }
  }

  function decrementChildren() {
    if (children > 0) {
      children--;
    }
  }

  function incrementInfants() {
    if (getTotalPassengers() < 10 && infants < adults && infants < 2) {
      infants++;
    }
  }

  function decrementInfants() {
    if (infants > 0) {
      infants--;
    }
  }

  function selectClass(className) {
    selectedClass = className;
  }

  function handleDone() {
    dispatch("done", {
      adults,
      children,
      infants,
      selectedClass,
      totalPassengers: getTotalPassengers(),
    });
    closeBottomSheet();
  }

  $: canAddPassengers = getTotalPassengers() < 10;
  $: canAddInfants = infants < adults && infants < 2 && canAddPassengers;
</script>

<!-- TRAVELLER SELECTION BOTTOMSHEET -->

<BottomSheet
  modelId={modalId}
  showCrossIcon={true}
  fullScreenModal={false}
  disableOuterClick={false}
>
  <div slot="title" class="text-xl font-bold text-gray-900">
    Select Travellers
  </div>

  <div slot="details" class="px-6 pb-6">
    <!-- PASSENGER SELECTION SECTION -->

    <div class="mb-8">
      <!-- Adults Counter -->
      <div
        class="flex justify-between items-center py-4 border-b border-gray-100"
      >
        <div>
          <div class="font-semibold text-gray-900">Adults</div>
          <div class="text-sm text-gray-500">12 years and above</div>
        </div>
        <div class="bg-whflex items-center">
          <div class="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              on:click={decrementAdults}
              disabled={adults <= 1}
              class="w-8 h-8 rounded-md flex items-center justify-center text-gray-600 hover:bg-white disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium"
            >
              −
            </button>
            <span
              class="select-none mx-4 font-semibold min-w-[20px] text-center"
              >{adults}</span
            >
            <button
              on:click={incrementAdults}
              disabled={!canAddPassengers || adults >= 9}
              class="w-8 h-8 rounded-md flex items-center justify-center text-gray-600 hover:bg-white disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium"
            >
              +
            </button>
          </div>
        </div>
      </div>

      <!-- Children Counter -->
      <div
        class="flex justify-between items-center py-4 border-b border-gray-100"
      >
        <div>
          <div class="font-semibold text-gray-900">Children</div>
          <div class="text-sm text-gray-500">2 to 12 years</div>
        </div>
        <div class="flex items-center">
          <div class="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              on:click={decrementChildren}
              disabled={children <= 0}
              class="w-8 h-8 rounded-md flex items-center justify-center text-gray-600 hover:bg-white disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium"
            >
              −
            </button>
            <span
              class="select-none mx-4 font-semibold min-w-[20px] text-center"
              >{children}</span
            >
            <button
              on:click={incrementChildren}
              disabled={!canAddPassengers || adults + children >= 9}
              class="w-8 h-8 rounded-md flex items-center justify-center text-gray-600 hover:bg-white disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium"
            >
              +
            </button>
          </div>
        </div>
      </div>

      <!-- Infants Counter -->
      <div
        class="flex justify-between items-center py-4 border-b border-gray-100"
      >
        <div>
          <div class="font-semibold text-gray-900">Infants</div>
          <div class="text-sm text-gray-500">
            Less than 2 years (max 1 per adult)
          </div>
        </div>
        <div class="flex items-center">
          <div class="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              on:click={decrementInfants}
              disabled={infants <= 0}
              class="w-8 h-8 rounded-md flex items-center justify-center text-gray-600 hover:bg-white disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium"
            >
              −
            </button>
            <span
              class="select-none mx-4 font-semibold min-w-[20px] text-center"
              >{infants}</span
            >
            <button
              on:click={incrementInfants}
              disabled={!canAddInfants}
              class="w-8 h-8 rounded-md flex items-center justify-center text-gray-600 hover:bg-white disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium"
            >
              +
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- CLASS SELECTION SECTION -->

    <div class="mb-8">
      <h2 class="select-none text-xl font-bold text-gray-900 mb-6">
        Select Class
      </h2>

      <div class="space-y-4">
        <!-- Economy Class -->
        <div
          on:click={() => selectClass("Economy Class")}
          class="select-none flex items-center cursor-pointer hover:bg-gray-50 p-2 -mx-2 rounded"
        >
          <div
            class={`w-5 h-5 rounded-full border-2 mr-4 ${
              selectedClass === "Economy Class"
                ? "border-blue-500 bg-blue-500 "
                : "border-gray-300"
            }`}
          >
            {#if selectedClass === "Economy Class"}
              <div class="w-2 h-2 bg-blue-500 rounded-full m-auto mt-0.5" />
            {/if}
          </div>
          <span class="select-none text-gray-900">Economy Class</span>
        </div>

        <!-- Business Class -->
        <div
          on:click={() => selectClass("Business Class")}
          class="flex items-center cursor-pointer hover:bg-gray-50 p-2 -mx-2 rounded"
        >
          <div
            class={`w-5 h-5 rounded-full border-2 mr-4 ${
              selectedClass === "Business Class"
                ? "border-blue-500 bg-blue-500"
                : "border-gray-300"
            }`}
          >
            {#if selectedClass === "Business Class"}
              <div class="w-2 h-2 bg-blue-500 rounded-full m-auto mt-0.5" />
            {/if}
          </div>
          <span class="select-none text-gray-900">Business Class</span>
        </div>

        <!-- Premium Economy Class -->
        <div
          on:click={() => selectClass("Premium Economy Class")}
          class="flex items-center cursor-pointer hover:bg-gray-50 p-2 -mx-2 rounded"
        >
          <div
            class={`w-5 h-5 rounded-full border-2 mr-4 ${
              selectedClass === "Premium Economy Class"
                ? "border-blue-500 bg-blue-500"
                : "border-gray-300"
            }`}
          >
            {#if selectedClass === "Premium Economy Class"}
              <div class="w-2 h-2 bg-blue-500 rounded-full m-auto mt-0.5" />
            {/if}
          </div>
          <span class="select-none text-gray-900">Premium Economy Class</span>
        </div>
      </div>
    </div>
    <div class="flex space-x-3 pt-4 border-t border-gray-200">
      <PrimaryButton
        on:submit={handleDone}
        designClass="select-none w-full select-none w-full bg-green-500 hover:bg-green-600 text-white py-4 rounded-lg font-semibold text-lg transition-colors"
      >
        Done
      </PrimaryButton>
    </div>
  </div>
</BottomSheet>

<style>
  /* Custom transitions for smooth interactions */
  button {
    transition: all 0.2s ease-in-out;
  }

  /* Ensure proper focus states for accessibility */
  button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  /* Smooth hover effects */
  [role="button"]:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
</style>
